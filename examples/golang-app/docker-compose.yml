version: '3.8'

services:
  golang-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: golang-fiber-api
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  # Optional: Add a reverse proxy (nginx)
  nginx:
    image: nginx:alpine
    container_name: golang-nginx-proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - golang-api
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - with-proxy

networks:
  app-network:
    driver: bridge
