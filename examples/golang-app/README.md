# Golang Fiber API - Docker Example

A simple REST API built with Go and Fiber framework, designed to demonstrate Docker containerization concepts.

## Features

- **Fast HTTP Framework**: Built with Fiber (Express-inspired framework for Go)
- **RESTful API**: CRUD operations for user management
- **Middleware Support**: CORS and logging middleware
- **Health Check**: Built-in health check endpoint
- **Docker Ready**: Multi-stage Dockerfile for optimized production builds
- **Security**: Non-root user execution in container

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Welcome message and available endpoints |
| GET | `/health` | Health check endpoint |
| GET | `/api/v1/users` | Get all users |
| GET | `/api/v1/users/:id` | Get user by ID |
| POST | `/api/v1/users` | Create new user |

## Running Locally

### Prerequisites
- Go 1.21 or higher
- Git

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd examples/golang-app

# Install dependencies
go mod download

# Run the application
go run main.go
```

The API will be available at `http://localhost:3000`

## Docker Usage

### Build Docker Image
```bash
# Build the image
docker build -t golang-fiber-api .

# Run the container
docker run -p 3000:3000 golang-fiber-api
```

### Using Docker Compose
```bash
# Run with docker-compose
docker-compose up -d

# Stop the services
docker-compose down
```

## API Examples

### Get all users
```bash
curl http://localhost:3000/api/v1/users
```

### Get user by ID
```bash
curl http://localhost:3000/api/v1/users/1
```

### Create new user
```bash
curl -X POST http://localhost:3000/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{"name":"Alice Johnson","email":"<EMAIL>"}'
```

### Health check
```bash
curl http://localhost:3000/health
```

## Docker Best Practices Demonstrated

1. **Multi-stage builds**: Separate build and runtime stages for smaller images
2. **Non-root user**: Security best practice
3. **Health checks**: Container health monitoring
4. **Minimal base image**: Using Alpine Linux for smaller footprint
5. **Layer optimization**: Copying dependencies before source code
6. **Security**: Running as non-privileged user

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `3000` | Port number for the server |

## Project Structure

```
golang-app/
├── main.go           # Main application file
├── Dockerfile        # Docker build instructions
├── .dockerignore     # Docker ignore file
├── docker-compose.yml # Docker Compose configuration
├── go.mod            # Go module file
├── go.sum            # Go dependencies checksum
└── README.md         # This file
```

## Learning Objectives

This example demonstrates:
- Building Go applications with Fiber
- Creating efficient Docker images
- Multi-stage Docker builds
- Container security best practices
- API development patterns
- Docker networking and port mapping
