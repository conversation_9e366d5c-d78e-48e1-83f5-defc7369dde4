package main

import (
	"log"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
)

// User struct for API responses
type User struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// In-memory user storage (for demo purposes)
var users = []User{
	{ID: 1, Name: "<PERSON>", Email: "<EMAIL>"},
	{ID: 2, Name: "<PERSON>", Email: "<EMAIL>"},
	{ID: 3, Name: "<PERSON>", Email: "<EMAIL>"},
}

func main() {
	// Create Fiber app
	app := fiber.New(fiber.Config{
		AppName: "Golang Docker Example API v1.0.0",
	})

	// Middleware
	app.Use(logger.New())
	app.Use(cors.New())

	// Routes
	setupRoutes(app)

	// Get port from environment variable or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}

	// Start server
	log.Printf("🚀 Server starting on port %s", port)
	log.Fatal(app.Listen(":" + port))
}

func setupRoutes(app *fiber.App) {
	// API v1 group
	api := app.Group("/api/v1")

	// Health check endpoint
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "Golang Docker Example API is running!",
			"version": "1.0.0",
		})
	})

	// Root endpoint
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message": "Welcome to Golang Docker Example API",
			"endpoints": []string{
				"GET /health - Health check",
				"GET /api/v1/users - Get all users",
				"GET /api/v1/users/:id - Get user by ID",
				"POST /api/v1/users - Create new user",
			},
		})
	})

	// User endpoints
	api.Get("/users", getUsers)
	api.Get("/users/:id", getUserByID)
	api.Post("/users", createUser)
}

// Get all users
func getUsers(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"success": true,
		"data":    users,
		"count":   len(users),
	})
}

// Get user by ID
func getUserByID(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid user ID",
		})
	}

	for _, user := range users {
		if user.ID == id {
			return c.JSON(fiber.Map{
				"success": true,
				"data":    user,
			})
		}
	}

	return c.Status(404).JSON(fiber.Map{
		"success": false,
		"error":   "User not found",
	})
}

// Create new user
func createUser(c *fiber.Ctx) error {
	var newUser User

	if err := c.BodyParser(&newUser); err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Cannot parse JSON",
		})
	}

	// Simple validation
	if newUser.Name == "" || newUser.Email == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Name and email are required",
		})
	}

	// Generate new ID
	newUser.ID = len(users) + 1
	users = append(users, newUser)

	return c.Status(201).JSON(fiber.Map{
		"success": true,
		"message": "User created successfully",
		"data":    newUser,
	})
}
