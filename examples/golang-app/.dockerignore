# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
main

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Docker files (optional, if you don't want to include them in the image)
# Dockerfile
# .dockerignore

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Environment files
.env
.env.local
.env.*.local
