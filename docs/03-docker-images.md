# Docker Images

## What are Docker Images?

Docker images are read-only templates used to create containers. They contain the application code, runtime, system tools, libraries, and settings needed to run an application. Images are built in layers, making them efficient and reusable.

## Image Architecture

### Layer System
Docker images use a layered file system where each layer represents a change or instruction:

```
┌─────────────────────────┐
│   Application Layer     │  ← Your app code
├─────────────────────────┤
│   Dependencies Layer    │  ← npm install, pip install
├─────────────────────────┤
│   Runtime Layer         │  ← Node.js, Python, etc.
├─────────────────────────┤
│   OS Layer              │  ← Ubuntu, Alpine, etc.
└─────────────────────────┘
```

### Benefits of Layered Architecture
- **Efficiency**: Layers are cached and reused
- **Storage**: Shared layers save disk space
- **Speed**: Only changed layers need to be rebuilt
- **Distribution**: Only new layers are downloaded

## Image Naming and Tagging

### Image Naming Convention
```
[registry]/[namespace]/[repository]:[tag]
```

### Examples
```bash
# Official images
nginx:latest
ubuntu:20.04
node:18-alpine

# User/organization images
docker.io/library/nginx:latest
mycompany/myapp:v1.0.0
registry.example.com/team/project:dev

# Local images
myapp:latest
myapp:v1.0
myapp:dev
```

### Tag Best Practices
- Use semantic versioning: `v1.0.0`, `v1.1.0`
- Environment tags: `dev`, `staging`, `prod`
- Avoid `latest` in production
- Use descriptive tags: `node-18-alpine`

## Working with Images

### Pulling Images
```bash
# Pull latest version
docker pull nginx

# Pull specific version
docker pull nginx:1.21-alpine

# Pull from specific registry
docker pull registry.example.com/myapp:v1.0

# Pull all tags of an image
docker pull -a nginx
```

### Listing Images
```bash
# List all images
docker images
docker image ls

# List with filters
docker images nginx
docker images --filter "dangling=true"
docker images --filter "before=nginx:latest"

# Custom format
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
```

### Image Information
```bash
# Inspect image details
docker inspect nginx:latest

# View image history (layers)
docker history nginx:latest

# Show image layers
docker image inspect nginx:latest --format='{{.RootFS.Layers}}'
```

## Building Images

### Using Dockerfile
```bash
# Build from current directory
docker build -t myapp .

# Build with specific Dockerfile
docker build -f Dockerfile.prod -t myapp:prod .

# Build with build arguments
docker build --build-arg VERSION=1.0 -t myapp .

# Build without cache
docker build --no-cache -t myapp .

# Build with target stage (multi-stage)
docker build --target production -t myapp:prod .
```

### Build Context
The build context is the set of files sent to Docker daemon:

```bash
# Current directory as context
docker build -t myapp .

# Specific directory as context
docker build -t myapp /path/to/context

# Remote Git repository
docker build -t myapp https://github.com/user/repo.git

# Exclude files with .dockerignore
echo "node_modules" > .dockerignore
echo "*.log" >> .dockerignore
```

### Multi-Stage Builds
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:18-alpine AS production
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## Image Optimization

### Size Optimization Techniques

#### 1. Use Minimal Base Images
```dockerfile
# Instead of
FROM ubuntu:20.04

# Use
FROM alpine:3.18
# or
FROM node:18-alpine
```

#### 2. Multi-Stage Builds
```dockerfile
# Build dependencies in one stage
FROM node:18 AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci

# Copy only production files
FROM node:18-alpine
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
```

#### 3. Minimize Layers
```dockerfile
# Instead of multiple RUN commands
RUN apt-get update
RUN apt-get install -y curl
RUN apt-get clean

# Combine into one
RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
```

#### 4. Use .dockerignore
```dockerignore
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
```

### Security Best Practices

#### 1. Use Official Images
```dockerfile
# Prefer official images
FROM node:18-alpine
FROM nginx:1.21-alpine
FROM postgres:14-alpine
```

#### 2. Don't Run as Root
```dockerfile
# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Switch to non-root user
USER nextjs
```

#### 3. Keep Images Updated
```bash
# Regularly update base images
docker pull node:18-alpine
docker build --no-cache -t myapp .
```

## Image Registry Operations

### Docker Hub
```bash
# Login to Docker Hub
docker login

# Tag image for registry
docker tag myapp:latest username/myapp:latest

# Push to Docker Hub
docker push username/myapp:latest

# Pull from Docker Hub
docker pull username/myapp:latest
```

### Private Registry
```bash
# Login to private registry
docker login registry.example.com

# Tag for private registry
docker tag myapp:latest registry.example.com/myapp:latest

# Push to private registry
docker push registry.example.com/myapp:latest
```

### Registry Management
```bash
# Search images on Docker Hub
docker search nginx

# Get image manifest
docker manifest inspect nginx:latest

# Remove image from local registry
docker rmi nginx:latest
```

## Image Maintenance

### Cleanup Operations
```bash
# Remove unused images
docker image prune

# Remove all unused images (including tagged)
docker image prune -a

# Remove specific image
docker rmi image_name:tag

# Remove multiple images
docker rmi $(docker images -q)

# Remove dangling images
docker images -f dangling=true -q | xargs docker rmi
```

### Image Analysis
```bash
# Analyze image size
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Check image vulnerabilities (if Docker Scout is available)
docker scout quickview nginx:latest

# Export image to tar file
docker save -o myapp.tar myapp:latest

# Import image from tar file
docker load -i myapp.tar
```

## Common Image Patterns

### Base Images by Use Case

#### Web Applications
```dockerfile
# Node.js applications
FROM node:18-alpine

# Python applications
FROM python:3.11-alpine

# Go applications
FROM golang:1.21-alpine AS builder
FROM alpine:latest AS runtime
```

#### Databases
```dockerfile
# PostgreSQL
FROM postgres:15-alpine

# MySQL
FROM mysql:8.0

# Redis
FROM redis:7-alpine
```

#### Web Servers
```dockerfile
# Nginx
FROM nginx:1.21-alpine

# Apache
FROM httpd:2.4-alpine
```

## Troubleshooting Images

### Common Issues
```bash
# Image not found
docker pull nginx:nonexistent-tag
# Error: manifest for nginx:nonexistent-tag not found

# Build context too large
docker build -t myapp .
# Solution: Use .dockerignore

# Layer caching issues
docker build --no-cache -t myapp .

# Permission issues
docker run --user $(id -u):$(id -g) myapp
```

### Debugging Techniques
```bash
# Run image interactively
docker run -it --entrypoint /bin/sh myapp:latest

# Check image layers
docker history myapp:latest

# Inspect image configuration
docker inspect myapp:latest
```

## Best Practices Summary

1. **Use official base images** when possible
2. **Keep images small** with multi-stage builds
3. **Use specific tags** instead of `latest`
4. **Minimize layers** by combining RUN commands
5. **Use .dockerignore** to exclude unnecessary files
6. **Don't run as root** user
7. **Regularly update** base images
8. **Scan for vulnerabilities** before deployment
9. **Use meaningful tags** for versioning
10. **Clean up unused images** regularly

## Next Steps

- Learn Dockerfile best practices
- Explore container registries
- Study image security scanning
- Practice multi-stage builds

---

*Understanding Docker images is fundamental to containerization. Practice building and optimizing images for better performance and security.*
