# Docker CLI Commands

## Overview

Docker CLI (Command Line Interface) is the primary way to interact with Dock<PERSON>. This document covers the most commonly used Docker commands with practical examples.

## Basic Docker Commands

### System Information
```bash
# Check Docker version
docker --version
docker version

# Display system-wide information
docker info

# Show Docker disk usage
docker system df

# Clean up unused resources
docker system prune
docker system prune -a  # Remove all unused images
```

## Image Management

### Pulling Images
```bash
# Pull an image from Docker Hub
docker pull nginx
docker pull nginx:latest
docker pull nginx:1.21-alpine

# Pull from specific registry
docker pull registry.example.com/myapp:v1.0
```

### Listing Images
```bash
# List all images
docker images
docker image ls

# List images with specific format
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# List dangling images
docker images -f dangling=true
```

### Building Images
```bash
# Build image from Dockerfile
docker build -t myapp .
docker build -t myapp:v1.0 .

# Build with specific Dockerfile
docker build -f Dockerfile.prod -t myapp:prod .

# Build without cache
docker build --no-cache -t myapp .

# Build with build arguments
docker build --build-arg VERSION=1.0 -t myapp .
```

### Removing Images
```bash
# Remove specific image
docker rmi nginx
docker rmi nginx:latest

# Remove multiple images
docker rmi nginx redis mysql

# Remove all unused images
docker image prune
docker image prune -a  # Remove all unused images
```

## Container Management

### Running Containers
```bash
# Run container in foreground
docker run nginx

# Run container in background (detached)
docker run -d nginx

# Run with port mapping
docker run -d -p 8080:80 nginx

# Run with name
docker run -d --name my-nginx nginx

# Run with environment variables
docker run -d -e ENV=production -e DEBUG=false myapp

# Run with volume mount
docker run -d -v /host/path:/container/path nginx

# Run interactively
docker run -it ubuntu bash
docker run -it --rm ubuntu bash  # Remove after exit
```

### Listing Containers
```bash
# List running containers
docker ps

# List all containers (including stopped)
docker ps -a

# List with specific format
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# List container IDs only
docker ps -q
```

### Container Operations
```bash
# Start stopped container
docker start container_name
docker start container_id

# Stop running container
docker stop container_name
docker stop container_id

# Restart container
docker restart container_name

# Pause/unpause container
docker pause container_name
docker unpause container_name

# Kill container (force stop)
docker kill container_name
```

### Executing Commands in Containers
```bash
# Execute command in running container
docker exec container_name ls -la

# Interactive shell in running container
docker exec -it container_name bash
docker exec -it container_name sh

# Execute as specific user
docker exec -it -u root container_name bash
```

### Container Logs
```bash
# View container logs
docker logs container_name

# Follow logs (real-time)
docker logs -f container_name

# Show last N lines
docker logs --tail 50 container_name

# Show logs with timestamps
docker logs -t container_name

# Show logs since specific time
docker logs --since 2023-01-01T10:00:00 container_name
```

### Container Information
```bash
# Inspect container details
docker inspect container_name

# Show container processes
docker top container_name

# Show container resource usage
docker stats container_name
docker stats  # All running containers

# Show container port mappings
docker port container_name
```

### Removing Containers
```bash
# Remove stopped container
docker rm container_name

# Force remove running container
docker rm -f container_name

# Remove multiple containers
docker rm container1 container2 container3

# Remove all stopped containers
docker container prune
```

## Volume Management

### Volume Operations
```bash
# Create volume
docker volume create my-volume

# List volumes
docker volume ls

# Inspect volume
docker volume inspect my-volume

# Remove volume
docker volume rm my-volume

# Remove unused volumes
docker volume prune
```

### Using Volumes
```bash
# Mount named volume
docker run -d -v my-volume:/data nginx

# Mount host directory (bind mount)
docker run -d -v /host/path:/container/path nginx

# Read-only mount
docker run -d -v /host/path:/container/path:ro nginx
```

## Network Management

### Network Operations
```bash
# List networks
docker network ls

# Create network
docker network create my-network
docker network create --driver bridge my-network

# Inspect network
docker network inspect my-network

# Remove network
docker network rm my-network

# Remove unused networks
docker network prune
```

### Container Networking
```bash
# Run container on specific network
docker run -d --network my-network nginx

# Connect container to network
docker network connect my-network container_name

# Disconnect container from network
docker network disconnect my-network container_name
```

## Docker Compose Commands

### Basic Compose Operations
```bash
# Start services
docker-compose up
docker-compose up -d  # Detached mode

# Stop services
docker-compose down

# View running services
docker-compose ps

# View logs
docker-compose logs
docker-compose logs service_name

# Build services
docker-compose build
docker-compose build service_name

# Scale services
docker-compose up -d --scale web=3
```

## Useful Command Combinations

### Cleanup Commands
```bash
# Stop all running containers
docker stop $(docker ps -q)

# Remove all stopped containers
docker rm $(docker ps -aq)

# Remove all images
docker rmi $(docker images -q)

# Complete cleanup
docker system prune -a --volumes
```

### Monitoring Commands
```bash
# Monitor all containers
docker stats

# Follow logs of multiple containers
docker-compose logs -f

# Watch container processes
watch docker ps
```

## Command Options Reference

### Common Flags
- `-d, --detach`: Run in background
- `-i, --interactive`: Keep STDIN open
- `-t, --tty`: Allocate pseudo-TTY
- `-p, --publish`: Publish ports
- `-v, --volume`: Mount volumes
- `-e, --env`: Set environment variables
- `--name`: Assign name to container
- `--rm`: Remove container when it exits
- `-f, --force`: Force operation

### Output Formatting
```bash
# Custom format examples
docker ps --format "{{.Names}}: {{.Status}}"
docker images --format "{{.Repository}}:{{.Tag}} - {{.Size}}"
```

## Best Practices

1. **Use specific image tags** instead of `latest`
2. **Clean up regularly** with `docker system prune`
3. **Use meaningful names** for containers and images
4. **Check logs** when containers fail to start
5. **Use multi-stage builds** for production images
6. **Limit resource usage** with `--memory` and `--cpus` flags
7. **Use health checks** for production containers

## Next Steps

- Learn about Dockerfile best practices
- Explore Docker Compose for multi-container applications
- Understand Docker networking in detail
- Study container security practices

---

*This covers the essential Docker CLI commands. Practice these commands with real containers to build muscle memory.*
