# Docker Volumes and Data Management

## Understanding Docker Data Storage

Docker containers are ephemeral by design - when a container is removed, all data stored in its writable layer is lost. Docker provides several mechanisms to persist data beyond the container lifecycle.

## Container Data Storage Layers

```
┌─────────────────────────────────────┐
│        Container Layer              │  ← Writable (temporary)
│        (Temporary Data)             │
├─────────────────────────────────────┤
│        Volume Mounts                │  ← Persistent Data
│        (Persistent Data)            │
├─────────────────────────────────────┤
│        Image Layers                 │  ← Read-only
│        (Application Code)           │
└─────────────────────────────────────┘
```

## Types of Data Storage

### 1. Container Layer (Temporary)
- **Location**: Inside container filesystem
- **Persistence**: Lost when container is removed
- **Use Case**: Temporary files, logs, cache
- **Performance**: Good (native filesystem)

### 2. Volumes (Recommended)
- **Location**: Docker-managed storage area
- **Persistence**: Survives container removal
- **Use Case**: Database data, user uploads, configuration
- **Performance**: Best (optimized by Docker)

### 3. Bind Mounts
- **Location**: Host filesystem path
- **Persistence**: Survives container removal
- **Use Case**: Development, configuration files
- **Performance**: Good (depends on host filesystem)

### 4. tmpfs Mounts
- **Location**: Host memory
- **Persistence**: Lost when container stops
- **Use Case**: Sensitive temporary data
- **Performance**: Excellent (in-memory)

## Docker Volumes

### What are Docker Volumes?

Docker volumes are the preferred mechanism for persisting data. They are completely managed by Docker and provide several advantages:

- **Decoupled from container lifecycle**
- **Can be shared between containers**
- **Backed up and restored easily**
- **Work on both Linux and Windows**
- **Can be stored on remote hosts or cloud**

### Volume Management Commands

```bash
# Create a volume
docker volume create my-volume
docker volume create --driver local my-volume

# List volumes
docker volume ls

# Inspect volume details
docker volume inspect my-volume

# Remove volume
docker volume rm my-volume

# Remove unused volumes
docker volume prune

# Remove all volumes
docker volume rm $(docker volume ls -q)
```

### Using Volumes with Containers

```bash
# Mount named volume
docker run -d -v my-volume:/data nginx

# Mount volume with specific options
docker run -d -v my-volume:/data:ro nginx  # read-only

# Mount multiple volumes
docker run -d \
  -v data-volume:/app/data \
  -v logs-volume:/app/logs \
  nginx

# Create and mount volume in one command
docker run -d -v new-volume:/data nginx
```

## Bind Mounts

### What are Bind Mounts?

Bind mounts allow you to mount a file or directory from the host machine into a container. The file or directory is referenced by its absolute path on the host machine.

### Using Bind Mounts

```bash
# Mount host directory
docker run -d -v /host/path:/container/path nginx

# Mount current directory
docker run -d -v $(pwd):/app nginx

# Mount with specific options
docker run -d -v /host/path:/container/path:ro nginx  # read-only
docker run -d -v /host/path:/container/path:rw nginx  # read-write (default)

# Mount single file
docker run -d -v /host/config.conf:/app/config.conf nginx
```

### Bind Mount Use Cases

```bash
# Development - source code mounting
docker run -d -v $(pwd)/src:/app/src node:alpine

# Configuration files
docker run -d -v /etc/nginx/nginx.conf:/etc/nginx/nginx.conf nginx

# Log files
docker run -d -v /var/log/app:/app/logs myapp

# Shared data between host and container
docker run -d -v /shared/data:/data ubuntu
```

## tmpfs Mounts

### What are tmpfs Mounts?

tmpfs mounts are stored in the host system's memory only and are never written to the host system's filesystem. They're useful for storing sensitive information temporarily.

### Using tmpfs Mounts

```bash
# Mount tmpfs
docker run -d --tmpfs /tmp nginx

# Mount with size limit
docker run -d --tmpfs /tmp:size=100m nginx

# Mount with specific options
docker run -d --tmpfs /tmp:rw,noexec,nosuid,size=100m nginx

# Multiple tmpfs mounts
docker run -d \
  --tmpfs /tmp \
  --tmpfs /var/cache \
  nginx
```

## Volume Drivers and Plugins

### Local Driver (Default)
```bash
# Create local volume
docker volume create --driver local my-volume

# Local volume with specific options
docker volume create \
  --driver local \
  --opt type=nfs \
  --opt o=addr=***********,rw \
  --opt device=:/path/to/dir \
  nfs-volume
```

### Third-Party Drivers
```bash
# AWS EBS volume
docker volume create \
  --driver rexray/ebs \
  --opt size=10 \
  ebs-volume

# Azure File volume
docker volume create \
  --driver azure/azurefile \
  --opt share=myshare \
  azure-volume

# GCP Persistent Disk
docker volume create \
  --driver gcp/gce-pd \
  --opt size=10GB \
  gcp-volume
```

## Data Management Patterns

### Database Data Persistence

```bash
# PostgreSQL with volume
docker run -d \
  --name postgres-db \
  -e POSTGRES_PASSWORD=password \
  -v postgres-data:/var/lib/postgresql/data \
  postgres:13

# MySQL with volume
docker run -d \
  --name mysql-db \
  -e MYSQL_ROOT_PASSWORD=password \
  -v mysql-data:/var/lib/mysql \
  mysql:8.0

# MongoDB with volume
docker run -d \
  --name mongo-db \
  -v mongo-data:/data/db \
  mongo:4.4
```

### Application Data Patterns

```bash
# Web application with uploads
docker run -d \
  --name webapp \
  -v app-uploads:/app/uploads \
  -v app-logs:/app/logs \
  mywebapp

# Configuration management
docker run -d \
  --name app \
  -v /host/config:/app/config:ro \
  -v app-data:/app/data \
  myapp

# Shared data between containers
docker volume create shared-data
docker run -d --name writer -v shared-data:/data writer-app
docker run -d --name reader -v shared-data:/data:ro reader-app
```

## Docker Compose and Volumes

### Named Volumes in Compose

```yaml
version: '3.8'

services:
  web:
    image: nginx
    volumes:
      - web-content:/usr/share/nginx/html
      - web-logs:/var/log/nginx

  database:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: password
    volumes:
      - db-data:/var/lib/postgresql/data

volumes:
  web-content:
  web-logs:
  db-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /host/db/data
```

### Bind Mounts in Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    volumes:
      # Bind mounts
      - ./src:/app/src                    # source code
      - ./config:/app/config:ro           # read-only config
      - /var/log/app:/app/logs            # log directory
      
      # Named volumes
      - app-data:/app/data
      - node-modules:/app/node_modules

volumes:
  app-data:
  node-modules:
```

### Development Environment Example

```yaml
version: '3.8'

services:
  # Frontend development
  frontend:
    build: ./frontend
    volumes:
      - ./frontend/src:/app/src           # hot reload
      - ./frontend/public:/app/public
      - frontend-modules:/app/node_modules
    ports:
      - "3000:3000"

  # Backend development
  backend:
    build: ./backend
    volumes:
      - ./backend:/app                    # source code
      - backend-modules:/app/node_modules
      - app-logs:/app/logs
    environment:
      - NODE_ENV=development
    ports:
      - "8000:8000"

  # Database with persistent data
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"

volumes:
  frontend-modules:
  backend-modules:
  app-logs:
  db-data:
```

## Volume Backup and Restore

### Backup Strategies

```bash
# Backup volume to tar file
docker run --rm \
  -v my-volume:/data \
  -v $(pwd):/backup \
  ubuntu tar czf /backup/backup.tar.gz -C /data .

# Backup database volume
docker run --rm \
  -v postgres-data:/var/lib/postgresql/data \
  -v $(pwd):/backup \
  postgres:13 pg_dumpall -U postgres > /backup/db-backup.sql

# Backup with timestamp
docker run --rm \
  -v my-volume:/data \
  -v $(pwd):/backup \
  ubuntu tar czf /backup/backup-$(date +%Y%m%d-%H%M%S).tar.gz -C /data .
```

### Restore Strategies

```bash
# Restore volume from tar file
docker run --rm \
  -v my-volume:/data \
  -v $(pwd):/backup \
  ubuntu tar xzf /backup/backup.tar.gz -C /data

# Restore database
docker run --rm \
  -v postgres-data:/var/lib/postgresql/data \
  -v $(pwd):/backup \
  postgres:13 psql -U postgres < /backup/db-backup.sql

# Copy data between volumes
docker run --rm \
  -v source-volume:/source \
  -v target-volume:/target \
  ubuntu cp -r /source/. /target/
```

## Performance Considerations

### Volume Performance Comparison

| Storage Type | Performance | Use Case |
|--------------|-------------|----------|
| **Volume** | Excellent | Production databases, persistent data |
| **Bind Mount** | Good | Development, configuration files |
| **tmpfs** | Excellent | Temporary data, sensitive information |
| **Container Layer** | Good | Temporary files, application logs |

### Optimization Tips

```bash
# Use volumes for database data
docker run -d -v db-data:/var/lib/mysql mysql

# Use tmpfs for temporary data
docker run -d --tmpfs /tmp myapp

# Use bind mounts for development only
docker run -d -v $(pwd):/app myapp  # development
docker run -d -v app-data:/app myapp  # production

# Avoid storing large files in container layer
# Use volumes or bind mounts instead
```

## Security Best Practices

### Volume Security

```bash
# Create volume with specific permissions
docker volume create --driver local \
  --opt type=tmpfs \
  --opt device=tmpfs \
  --opt o=size=100m,uid=1000,gid=1000 \
  secure-volume

# Mount volume as read-only when possible
docker run -d -v config-volume:/app/config:ro myapp

# Use non-root user with volumes
docker run -d \
  --user 1001:1001 \
  -v app-data:/app/data \
  myapp
```

### Bind Mount Security

```bash
# Avoid mounting sensitive host directories
# Bad: docker run -v /etc:/host-etc myapp
# Good: docker run -v /specific/config:/app/config myapp

# Use read-only mounts when possible
docker run -d -v /host/config:/app/config:ro myapp

# Be careful with bind mount permissions
docker run -d \
  --user $(id -u):$(id -g) \
  -v $(pwd):/app \
  myapp
```

## Troubleshooting

### Common Issues

```bash
# Volume not found
docker volume ls
docker volume inspect volume-name

# Permission denied
docker exec -it container-name ls -la /data
docker run --rm -v my-volume:/data ubuntu chown -R 1000:1000 /data

# Disk space issues
docker system df
docker volume prune

# Mount point issues
docker inspect container-name | grep -A 10 "Mounts"

# Data not persisting
docker volume ls
docker inspect volume-name
```

### Debugging Commands

```bash
# Check volume usage
docker system df -v

# Inspect volume details
docker volume inspect my-volume

# Check container mounts
docker inspect container-name --format='{{.Mounts}}'

# Access volume data directly
docker run --rm -it -v my-volume:/data ubuntu bash

# Compare volume contents
docker run --rm -v vol1:/vol1 -v vol2:/vol2 ubuntu diff -r /vol1 /vol2
```

## Best Practices Summary

1. **Use volumes for persistent data** (databases, user uploads)
2. **Use bind mounts for development** (source code, configuration)
3. **Use tmpfs for sensitive temporary data**
4. **Always backup important volumes**
5. **Use named volumes in production**
6. **Avoid storing data in container layer**
7. **Set appropriate permissions** on mounted data
8. **Use read-only mounts** when data shouldn't be modified
9. **Monitor disk usage** regularly
10. **Clean up unused volumes** periodically

---

*Proper data management is crucial for production Docker applications. Master these concepts to ensure data persistence and reliability.*
