# Docker Networks

## Understanding Docker Networking

Docker networking enables containers to communicate with each other, the host system, and external networks. Docker provides several networking options to suit different use cases, from simple single-host deployments to complex multi-host orchestrated environments.

## Docker Network Architecture

```
┌─────────────────────────────────────────────────────┐
│                 Host System                         │
├─────────────────────────────────────────────────────┤
│                Docker Engine                        │
├─────────────────────────────────────────────────────┤
│  Bridge Network  │  Host Network  │  Custom Networks │
├─────────────────────────────────────────────────────┤
│  Container 1     │  Container 2   │  Container 3     │
│  IP: **********  │  Host IP       │  IP: *********** │
└─────────────────────────────────────────────────────┘
```

## Network Drivers

### 1. Bridge (Default)
- **Use Case**: Single-host container communication
- **Isolation**: Containers on same bridge can communicate
- **External Access**: Port mapping required
- **Default**: All containers use bridge network by default

### 2. Host
- **Use Case**: Maximum network performance
- **Isolation**: No network isolation from host
- **External Access**: Direct access to host network
- **Limitation**: Port conflicts possible

### 3. None
- **Use Case**: Complete network isolation
- **Isolation**: No network access
- **External Access**: None
- **Use Case**: Security-sensitive applications

### 4. Overlay
- **Use Case**: Multi-host container communication
- **Isolation**: Secure communication across hosts
- **External Access**: Load balancer integration
- **Requirement**: Docker Swarm or external key-value store

### 5. Macvlan
- **Use Case**: Legacy applications requiring MAC addresses
- **Isolation**: Each container gets unique MAC address
- **External Access**: Direct network access
- **Requirement**: Physical network configuration

## Default Networks

### Bridge Network
```bash
# List networks
docker network ls

# Inspect default bridge
docker network inspect bridge

# Run container on default bridge
docker run -d --name web nginx

# Check container IP
docker inspect web --format='{{.NetworkSettings.IPAddress}}'
```

### Network Information
```bash
# Default networks created by Docker:
# - bridge (default for containers)
# - host (use host networking)
# - none (no networking)

# View network details
docker network inspect bridge
docker network inspect host
docker network inspect none
```

## Custom Networks

### Creating Custom Networks

```bash
# Create bridge network
docker network create my-network

# Create network with specific driver
docker network create --driver bridge my-bridge-network

# Create network with custom subnet
docker network create \
  --driver bridge \
  --subnet=***********/24 \
  --ip-range=***********28/25 \
  --gateway=*********** \
  custom-network

# Create network with DNS options
docker network create \
  --driver bridge \
  --opt com.docker.network.bridge.name=my-bridge \
  --opt com.docker.network.driver.mtu=1200 \
  advanced-network
```

### Network Management

```bash
# List networks
docker network ls

# Inspect network
docker network inspect my-network

# Remove network
docker network rm my-network

# Remove unused networks
docker network prune

# Remove all custom networks
docker network rm $(docker network ls -q --filter type=custom)
```

## Container Networking

### Connecting Containers to Networks

```bash
# Run container on specific network
docker run -d --name web --network my-network nginx

# Run container on multiple networks
docker run -d --name app \
  --network frontend \
  --network backend \
  myapp

# Connect running container to network
docker network connect my-network existing-container

# Disconnect container from network
docker network disconnect my-network existing-container

# Connect with specific IP
docker network connect --ip ************* my-network container-name
```

### Container Communication

```bash
# Create custom network
docker network create app-network

# Run database container
docker run -d \
  --name database \
  --network app-network \
  -e POSTGRES_PASSWORD=password \
  postgres:13

# Run application container
docker run -d \
  --name webapp \
  --network app-network \
  -e DATABASE_URL=********************************************/mydb \
  myapp

# Containers can communicate using container names as hostnames
# webapp can connect to database using hostname "database"
```

## Network Communication Examples

### Web Application Stack

```bash
# Create networks
docker network create frontend
docker network create backend

# Database (backend only)
docker run -d \
  --name postgres \
  --network backend \
  -e POSTGRES_PASSWORD=password \
  postgres:13

# API Server (both networks)
docker run -d \
  --name api \
  --network backend \
  myapi

docker network connect frontend api

# Web Server (frontend only)
docker run -d \
  --name nginx \
  --network frontend \
  -p 80:80 \
  nginx

# Redis Cache (backend only)
docker run -d \
  --name redis \
  --network backend \
  redis:alpine
```

### Microservices Communication

```bash
# Create service network
docker network create microservices

# User Service
docker run -d \
  --name user-service \
  --network microservices \
  user-service:latest

# Product Service
docker run -d \
  --name product-service \
  --network microservices \
  product-service:latest

# Order Service (can communicate with both)
docker run -d \
  --name order-service \
  --network microservices \
  -e USER_SERVICE_URL=http://user-service:3000 \
  -e PRODUCT_SERVICE_URL=http://product-service:3000 \
  order-service:latest

# API Gateway (exposed to external)
docker run -d \
  --name api-gateway \
  --network microservices \
  -p 8080:8080 \
  api-gateway:latest
```

## Docker Compose Networking

### Default Network Behavior

```yaml
version: '3.8'

services:
  web:
    image: nginx
    ports:
      - "80:80"
  
  app:
    build: .
    # Both services are on the same default network
    # app can reach web using hostname "web"
  
  database:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: password
    # app can reach database using hostname "database"
```

### Custom Networks in Compose

```yaml
version: '3.8'

services:
  # Frontend services
  web:
    image: nginx
    networks:
      - frontend
    ports:
      - "80:80"

  # Application services
  app:
    build: .
    networks:
      - frontend
      - backend
    environment:
      - DATABASE_URL=********************************************/mydb

  # Backend services
  database:
    image: postgres:13
    networks:
      - backend
    environment:
      POSTGRES_PASSWORD: password

  redis:
    image: redis:alpine
    networks:
      - backend

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true  # No external access
```

### Advanced Network Configuration

```yaml
version: '3.8'

services:
  app:
    build: .
    networks:
      app-network:
        ipv4_address: *************
        aliases:
          - api-server
          - backend-service

networks:
  app-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ***********/24
          gateway: ***********
    driver_opts:
      com.docker.network.bridge.name: app-bridge
      com.docker.network.driver.mtu: 1200
```

## Network Security

### Network Isolation

```bash
# Create isolated internal network
docker network create \
  --internal \
  --subnet=********/24 \
  isolated-network

# Containers on internal network cannot reach external internet
docker run -d \
  --name secure-app \
  --network isolated-network \
  myapp
```

### Firewall and Access Control

```yaml
version: '3.8'

services:
  # Public-facing service
  web:
    image: nginx
    networks:
      - public
    ports:
      - "80:80"
      - "443:443"

  # Internal services (no external access)
  app:
    build: .
    networks:
      - internal
    # No ports exposed

  database:
    image: postgres:13
    networks:
      - internal
    environment:
      POSTGRES_PASSWORD: password
    # No external access

networks:
  public:
    driver: bridge
  internal:
    driver: bridge
    internal: true  # No internet access
```

### Network Encryption

```bash
# Create encrypted overlay network (requires Swarm)
docker network create \
  --driver overlay \
  --opt encrypted \
  secure-overlay

# Use with Docker Swarm services
docker service create \
  --name secure-service \
  --network secure-overlay \
  myapp
```

## Advanced Networking

### Host Network Mode

```bash
# Use host networking (no isolation)
docker run -d --network host nginx

# Container uses host's network stack directly
# No port mapping needed, but less secure
```

### None Network Mode

```bash
# No networking
docker run -d --network none myapp

# Container has no network access
# Useful for batch processing or security-sensitive apps
```

### Container Network Mode

```bash
# Share network with another container
docker run -d --name web nginx
docker run -d --network container:web myapp

# myapp shares web's network stack
# Both containers have same IP address
```

## Network Troubleshooting

### Diagnostic Commands

```bash
# List networks and their containers
docker network ls
docker network inspect network-name

# Check container network settings
docker inspect container-name --format='{{.NetworkSettings}}'

# Test connectivity between containers
docker exec container1 ping container2
docker exec container1 nslookup container2
docker exec container1 telnet container2 80

# Check port bindings
docker port container-name
netstat -tulpn | grep docker

# Monitor network traffic
docker exec container-name netstat -i
docker exec container-name ss -tuln
```

### Common Network Issues

```bash
# Container cannot reach another container
# Check if they're on the same network
docker network inspect network-name

# Port binding conflicts
# Check what's using the port
netstat -tulpn | grep :8080
docker ps --format "table {{.Names}}\t{{.Ports}}"

# DNS resolution issues
docker exec container-name nslookup container2
docker exec container-name cat /etc/resolv.conf

# Network connectivity issues
docker exec container-name ping *******  # External connectivity
docker exec container-name ping gateway-ip  # Gateway connectivity
```

### Network Performance Testing

```bash
# Test network performance between containers
docker run --rm --network my-network nicolaka/netshoot iperf3 -s
docker run --rm --network my-network nicolaka/netshoot iperf3 -c server-container

# Network debugging container
docker run --rm -it --network my-network nicolaka/netshoot

# Inside netshoot container:
# ping, traceroute, nslookup, dig, curl, wget, etc.
```

## Best Practices

### Network Design Principles

1. **Principle of Least Privilege**: Only expose necessary ports
2. **Network Segmentation**: Separate frontend, backend, and database networks
3. **Use Custom Networks**: Avoid default bridge for production
4. **Internal Networks**: Use internal networks for backend services
5. **Meaningful Names**: Use descriptive network and container names

### Security Best Practices

```bash
# Create separate networks for different tiers
docker network create frontend
docker network create backend
docker network create --internal database

# Use internal networks for sensitive services
docker network create --internal secure-backend

# Limit port exposure
docker run -d -p 127.0.0.1:8080:8080 myapp  # Bind to localhost only

# Use non-default ports when possible
docker run -d -p 8443:443 nginx  # Use non-standard port
```

### Production Considerations

```yaml
version: '3.8'

services:
  web:
    image: nginx:alpine
    networks:
      - frontend
    ports:
      - "80:80"
      - "443:443"
    restart: unless-stopped

  app:
    build: .
    networks:
      - frontend
      - backend
    restart: unless-stopped
    deploy:
      replicas: 3

  database:
    image: postgres:13-alpine
    networks:
      - backend
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
    volumes:
      - db_data:/var/lib/postgresql/data
    restart: unless-stopped
    secrets:
      - db_password

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

volumes:
  db_data:

secrets:
  db_password:
    external: true
```

## Monitoring and Logging

### Network Monitoring

```bash
# Monitor network usage
docker stats --format "table {{.Container}}\t{{.NetIO}}"

# Network interface statistics
docker exec container-name cat /proc/net/dev

# Connection tracking
docker exec container-name netstat -an
docker exec container-name ss -tuln
```

### Network Logging

```bash
# Enable network debugging
docker run -d --log-driver json-file --log-opt max-size=10m myapp

# Monitor network events
docker events --filter type=network

# Container network logs
docker logs container-name | grep -i network
```

---

*Docker networking is essential for building scalable, secure containerized applications. Master these concepts to design robust network architectures.*
