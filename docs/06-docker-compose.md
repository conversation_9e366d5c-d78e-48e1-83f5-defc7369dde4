# Docker Compose - Multi-Container Applications

## What is Docker Compose?

Docker Compose is a tool for defining and running multi-container Docker applications. With Compose, you use a YAML file to configure your application's services, networks, and volumes, then create and start all services with a single command.

## Why Use Docker Compose?

### Benefits
- **Simplified Management**: Manage multiple containers as a single application
- **Environment Consistency**: Same configuration across development, testing, and production
- **Service Dependencies**: Define startup order and dependencies between services
- **Network Isolation**: Automatic network creation for service communication
- **Volume Management**: Persistent data storage across container restarts
- **Scaling**: Easy horizontal scaling of services

### Use Cases
- **Development Environments**: Local development with databases, caches, etc.
- **Testing**: Integration testing with multiple services
- **Microservices**: Orchestrating microservice architectures
- **CI/CD Pipelines**: Automated testing and deployment

## Docker Compose File Structure

### Basic docker-compose.yml
```yaml
version: '3.8'

services:
  web:
    image: nginx:alpine
    ports:
      - "8080:80"
    
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
```

### Complete Structure
```yaml
version: '3.8'

services:
  # Service definitions
  
networks:
  # Custom networks
  
volumes:
  # Named volumes
  
configs:
  # Configuration files
  
secrets:
  # Sensitive data
```

## Service Configuration

### Image and Build
```yaml
services:
  # Using pre-built image
  web:
    image: nginx:alpine
    
  # Building from Dockerfile
  app:
    build: .
    
  # Build with context and Dockerfile
  api:
    build:
      context: ./api
      dockerfile: Dockerfile.prod
      args:
        - VERSION=1.0.0
```

### Ports and Networking
```yaml
services:
  web:
    image: nginx
    ports:
      - "8080:80"        # host:container
      - "8443:443"
      - "127.0.0.1:9000:9000"  # bind to specific interface
    expose:
      - "3000"           # expose to other services only
```

### Environment Variables
```yaml
services:
  app:
    image: myapp
    environment:
      - NODE_ENV=production
      - DEBUG=false
      - DATABASE_URL=******************************/myapp
    env_file:
      - .env
      - .env.production
```

### Volumes and Storage
```yaml
services:
  app:
    image: myapp
    volumes:
      - ./src:/app/src           # bind mount
      - node_modules:/app/node_modules  # named volume
      - /var/log:/app/logs:ro    # read-only mount
      - type: tmpfs              # temporary filesystem
        target: /tmp
        tmpfs:
          size: 100M

volumes:
  node_modules:    # named volume definition
  app_data:
    driver: local
```

### Dependencies and Startup Order
```yaml
services:
  web:
    image: nginx
    depends_on:
      - app
      - database
    
  app:
    build: .
    depends_on:
      database:
        condition: service_healthy
    
  database:
    image: postgres:13
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## Real-World Examples

### Web Application Stack
```yaml
version: '3.8'

services:
  # Frontend (React/Vue/Angular)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend

  # Backend API (Node.js/Python/Go)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/myapp
      - REDIS_URL=redis://redis:6379
    depends_on:
      - database
      - redis

  # Database
  database:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  # Cache
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge
```

### Microservices Architecture
```yaml
version: '3.8'

services:
  # API Gateway
  gateway:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - user-service
      - product-service
      - order-service

  # User Service
  user-service:
    build: ./services/user
    environment:
      - DATABASE_URL=*******************************************/users
    depends_on:
      - user-db

  user-db:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: users
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - user_data:/var/lib/postgresql/data

  # Product Service
  product-service:
    build: ./services/product
    environment:
      - DATABASE_URL=**********************************************/products
    depends_on:
      - product-db

  product-db:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: products
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - product_data:/var/lib/postgresql/data

  # Order Service
  order-service:
    build: ./services/order
    environment:
      - DATABASE_URL=********************************************/orders
      - USER_SERVICE_URL=http://user-service:3000
      - PRODUCT_SERVICE_URL=http://product-service:3000
    depends_on:
      - order-db
      - user-service
      - product-service

  order-db:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: orders
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - order_data:/var/lib/postgresql/data

volumes:
  user_data:
  product_data:
  order_data:
```

### Development Environment
```yaml
version: '3.8'

services:
  # Main application
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/myapp_dev
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  # Database
  postgres:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: myapp_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Cache
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  # Database Admin
  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "8080:80"
    depends_on:
      - postgres

volumes:
  postgres_dev_data:
```

## Docker Compose Commands

### Basic Operations
```bash
# Start services
docker-compose up
docker-compose up -d          # detached mode
docker-compose up --build     # rebuild images

# Stop services
docker-compose down
docker-compose down -v        # remove volumes
docker-compose down --rmi all # remove images

# View running services
docker-compose ps

# View logs
docker-compose logs
docker-compose logs -f        # follow logs
docker-compose logs web       # specific service
```

### Service Management
```bash
# Start specific service
docker-compose up web

# Stop specific service
docker-compose stop web

# Restart services
docker-compose restart
docker-compose restart web

# Scale services
docker-compose up -d --scale web=3

# Execute commands
docker-compose exec web bash
docker-compose exec web ls -la

# Run one-off commands
docker-compose run web npm test
docker-compose run --rm web npm install
```

### Build and Images
```bash
# Build services
docker-compose build
docker-compose build web     # specific service
docker-compose build --no-cache

# Pull images
docker-compose pull

# Push images
docker-compose push
```

## Advanced Features

### Multiple Compose Files
```bash
# Base configuration
docker-compose.yml

# Development overrides
docker-compose.override.yml

# Production overrides
docker-compose.prod.yml

# Use specific files
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
```

### Environment-Specific Configurations
```yaml
# docker-compose.yml (base)
version: '3.8'
services:
  app:
    build: .
    environment:
      - NODE_ENV=${NODE_ENV:-development}

# docker-compose.override.yml (development)
version: '3.8'
services:
  app:
    volumes:
      - .:/app
    ports:
      - "3000:3000"

# docker-compose.prod.yml (production)
version: '3.8'
services:
  app:
    restart: unless-stopped
    environment:
      - NODE_ENV=production
```

### Health Checks and Dependencies
```yaml
services:
  web:
    image: nginx
    depends_on:
      app:
        condition: service_healthy
    
  app:
    build: .
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - database
    
  database:
    image: postgres:13
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## Best Practices

### File Organization
```
project/
├── docker-compose.yml
├── docker-compose.override.yml
├── docker-compose.prod.yml
├── .env
├── .env.example
├── services/
│   ├── web/
│   │   └── Dockerfile
│   ├── api/
│   │   └── Dockerfile
│   └── worker/
│       └── Dockerfile
└── config/
    ├── nginx.conf
    └── init.sql
```

### Security Best Practices
```yaml
services:
  app:
    build: .
    user: "1001:1001"          # non-root user
    read_only: true            # read-only filesystem
    tmpfs:
      - /tmp
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    security_opt:
      - no-new-privileges:true
```

### Performance Optimization
```yaml
services:
  app:
    build: .
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## Troubleshooting

### Common Issues
```bash
# Port already in use
docker-compose down
netstat -tulpn | grep :8080

# Service won't start
docker-compose logs service_name
docker-compose exec service_name bash

# Network issues
docker network ls
docker network inspect project_default

# Volume issues
docker volume ls
docker volume inspect project_volume_name

# Clean up everything
docker-compose down -v --rmi all
docker system prune -a
```

### Debugging Tips
```bash
# Validate compose file
docker-compose config

# Check service status
docker-compose ps

# Follow all logs
docker-compose logs -f

# Execute into running service
docker-compose exec service_name bash

# Run service with different command
docker-compose run --rm service_name bash
```

---

*Docker Compose simplifies multi-container application management. Master these concepts to build complex, scalable applications.*
