# Docker Security

## Docker Security Overview

Container security is a shared responsibility between the container platform, the host system, and the application. Docker provides multiple layers of security, but proper configuration and best practices are essential for a secure containerized environment.

## Security Layers

```
┌─────────────────────────────────────────┐
│           Application Security          │  ← Code, Dependencies, Config
├─────────────────────────────────────────┤
│           Container Security            │  ← Runtime, Isolation, Resources
├─────────────────────────────────────────┤
│            Image Security               │  ← Base Images, Vulnerabilities
├─────────────────────────────────────────┤
│            Host Security                │  ← OS, Docker Daemon, Network
├─────────────────────────────────────────┤
│         Infrastructure Security         │  ← Physical, Cloud, Access
└─────────────────────────────────────────┘
```

## Container Runtime Security

### User and Privilege Management

#### Running as Non-Root User
```dockerfile
# Create non-root user in Dockerfile
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Switch to non-root user
USER appuser

# Alternative: Use numeric UID
USER 1001:1001
```

```bash
# Run container as specific user
docker run -d --user 1001:1001 nginx

# Run as current user (for development)
docker run -d --user $(id -u):$(id -g) myapp

# Check user inside container
docker exec container-name whoami
docker exec container-name id
```

#### Dropping Capabilities
```bash
# Drop all capabilities and add only necessary ones
docker run -d \
  --cap-drop ALL \
  --cap-add NET_BIND_SERVICE \
  nginx

# Common capabilities to drop
docker run -d \
  --cap-drop ALL \
  --cap-add CHOWN \
  --cap-add DAC_OVERRIDE \
  --cap-add SETGID \
  --cap-add SETUID \
  myapp

# List available capabilities
docker run --rm alpine sh -c 'apk add libcap && capsh --print'
```

#### Security Options
```bash
# Disable new privileges
docker run -d --security-opt no-new-privileges nginx

# AppArmor profile
docker run -d --security-opt apparmor:docker-default nginx

# SELinux labels
docker run -d --security-opt label:type:container_t nginx

# Seccomp profile
docker run -d --security-opt seccomp:default.json nginx
```

### Resource Limits and Isolation

#### Memory and CPU Limits
```bash
# Limit memory usage
docker run -d --memory="512m" nginx

# Limit CPU usage
docker run -d --cpus="1.5" nginx

# Limit both memory and CPU
docker run -d \
  --memory="1g" \
  --cpus="2" \
  --memory-swap="2g" \
  nginx

# Set CPU priority
docker run -d --cpu-shares=512 nginx
```

#### Process Limits
```bash
# Limit number of processes
docker run -d --pids-limit=100 nginx

# Limit file descriptors
docker run -d --ulimit nofile=1024:2048 nginx

# Multiple ulimits
docker run -d \
  --ulimit nofile=1024:2048 \
  --ulimit nproc=512:1024 \
  nginx
```

#### Read-Only Filesystem
```bash
# Mount root filesystem as read-only
docker run -d --read-only nginx

# Read-only with writable tmp
docker run -d \
  --read-only \
  --tmpfs /tmp \
  --tmpfs /var/cache \
  nginx

# Read-only with specific writable volumes
docker run -d \
  --read-only \
  -v /app/logs:/app/logs \
  --tmpfs /tmp \
  myapp
```

## Image Security

### Base Image Security

#### Use Official Images
```dockerfile
# Good: Official images
FROM node:18-alpine
FROM nginx:alpine
FROM postgres:13-alpine

# Better: Specific versions
FROM node:18.17.0-alpine3.18

# Best: Distroless images for production
FROM gcr.io/distroless/nodejs18-debian11
```

#### Minimal Base Images
```dockerfile
# Alpine Linux (small, security-focused)
FROM alpine:3.18

# Distroless (no shell, package manager)
FROM gcr.io/distroless/java:11

# Scratch (empty base image)
FROM scratch
COPY myapp /
ENTRYPOINT ["/myapp"]
```

### Vulnerability Scanning

#### Docker Scout (Built-in)
```bash
# Scan image for vulnerabilities
docker scout quickview nginx:latest

# Detailed vulnerability report
docker scout cves nginx:latest

# Compare images
docker scout compare --to nginx:alpine nginx:latest

# Scan local image
docker scout quickview myapp:latest
```

#### Third-Party Scanners
```bash
# Trivy scanner
trivy image nginx:latest

# Clair scanner
clair-scanner nginx:latest

# Snyk scanner
snyk container test nginx:latest
```

### Secure Image Building

#### Multi-Stage Builds for Security
```dockerfile
# Build stage with development tools
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage with minimal dependencies
FROM node:18-alpine AS production
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs package*.json ./

USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```

#### Secrets Management
```dockerfile
# Bad: Hardcoded secrets
ENV API_KEY=secret123

# Good: Use build secrets
RUN --mount=type=secret,id=api_key \
    API_KEY=$(cat /run/secrets/api_key) && \
    # Use API_KEY for build process
```

```bash
# Build with secrets
echo "secret123" | docker build --secret id=api_key,src=- .

# Use Docker secrets in Swarm
docker secret create api_key api_key.txt
docker service create --secret api_key myapp
```

## Network Security

### Network Isolation
```bash
# Create isolated internal network
docker network create \
  --internal \
  --subnet=********/24 \
  secure-network

# Containers on internal network cannot reach internet
docker run -d \
  --name secure-app \
  --network secure-network \
  myapp
```

### Port Security
```bash
# Bind to localhost only
docker run -d -p 127.0.0.1:8080:8080 myapp

# Use non-standard ports
docker run -d -p 8443:443 nginx

# Limit port exposure
docker run -d \
  --expose 3000 \
  --network custom-network \
  myapp  # No external port mapping
```

### TLS/SSL Configuration
```yaml
# docker-compose.yml with TLS
version: '3.8'

services:
  web:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl/cert.pem:/etc/ssl/certs/cert.pem:ro
      - ./ssl/key.pem:/etc/ssl/private/key.pem:ro
      - ./nginx-ssl.conf:/etc/nginx/nginx.conf:ro
    networks:
      - frontend

  app:
    build: .
    networks:
      - frontend
      - backend
    environment:
      - HTTPS_ONLY=true

networks:
  frontend:
  backend:
    internal: true
```

## Host Security

### Docker Daemon Security

#### Daemon Configuration
```json
// /etc/docker/daemon.json
{
  "icc": false,
  "userland-proxy": false,
  "no-new-privileges": true,
  "seccomp-profile": "/etc/docker/seccomp.json",
  "selinux-enabled": true,
  "userns-remap": "default"
}
```

#### TLS for Docker Daemon
```bash
# Generate certificates
openssl genrsa -aes256 -out ca-key.pem 4096
openssl req -new -x509 -days 365 -key ca-key.pem -sha256 -out ca.pem

# Configure daemon with TLS
dockerd \
  --tlsverify \
  --tlscacert=ca.pem \
  --tlscert=server-cert.pem \
  --tlskey=server-key.pem \
  -H=0.0.0.0:2376

# Client connection with TLS
docker --tlsverify \
  --tlscacert=ca.pem \
  --tlscert=cert.pem \
  --tlskey=key.pem \
  -H=daemon-host:2376 version
```

### User Namespace Remapping
```bash
# Enable user namespace remapping
echo 'dockremap:165536:65536' >> /etc/subuid
echo 'dockremap:165536:65536' >> /etc/subgid

# Configure daemon
echo '{"userns-remap": "default"}' > /etc/docker/daemon.json
systemctl restart docker

# Root inside container maps to unprivileged user on host
```

## Secrets Management

### Docker Secrets (Swarm Mode)
```bash
# Create secret
echo "mysecretpassword" | docker secret create db_password -

# Use secret in service
docker service create \
  --name webapp \
  --secret db_password \
  myapp

# Access secret in container (mounted at /run/secrets/)
cat /run/secrets/db_password
```

### Environment Variables Security
```bash
# Bad: Sensitive data in environment variables
docker run -d -e PASSWORD=secret123 myapp

# Good: Use secrets or files
docker run -d \
  -v /secure/config:/app/config:ro \
  myapp

# Good: Use init containers for secret retrieval
docker run -d \
  --init \
  -v secret-volume:/secrets \
  myapp
```

### External Secret Management
```yaml
# Using external secret management
version: '3.8'

services:
  app:
    image: myapp
    environment:
      - VAULT_ADDR=https://vault.example.com
      - VAULT_TOKEN_FILE=/run/secrets/vault_token
    secrets:
      - vault_token
    command: |
      sh -c '
        export VAULT_TOKEN=$$(cat /run/secrets/vault_token)
        export DB_PASSWORD=$$(vault kv get -field=password secret/db)
        exec myapp
      '

secrets:
  vault_token:
    external: true
```

## Security Scanning and Monitoring

### Runtime Security Monitoring
```bash
# Monitor container behavior
docker run -d \
  --name security-monitor \
  --privileged \
  -v /var/run/docker.sock:/var/run/docker.sock \
  falcosecurity/falco

# Container resource monitoring
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Process monitoring
docker exec container-name ps aux
docker top container-name
```

### Log Security
```bash
# Secure logging configuration
docker run -d \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  --log-opt labels=security \
  myapp

# Centralized logging
docker run -d \
  --log-driver syslog \
  --log-opt syslog-address=tcp://log-server:514 \
  --log-opt tag="{{.Name}}" \
  myapp
```

## Security Best Practices

### Dockerfile Security
```dockerfile
# Security-hardened Dockerfile
FROM node:18-alpine

# Update packages and remove package manager
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy and install dependencies as root
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY --chown=nextjs:nodejs . .

# Switch to non-root user
USER nextjs

# Use init system
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node healthcheck.js

# Start application
CMD ["node", "server.js"]
```

### Production Security Checklist

#### Container Configuration
- [ ] Run as non-root user
- [ ] Use read-only filesystem
- [ ] Drop unnecessary capabilities
- [ ] Set resource limits
- [ ] Enable security options
- [ ] Use minimal base images
- [ ] Scan images for vulnerabilities

#### Network Security
- [ ] Use custom networks
- [ ] Implement network segmentation
- [ ] Enable TLS/SSL
- [ ] Limit port exposure
- [ ] Use internal networks for backend services

#### Secrets Management
- [ ] Never hardcode secrets
- [ ] Use Docker secrets or external secret management
- [ ] Rotate secrets regularly
- [ ] Limit secret access

#### Monitoring and Logging
- [ ] Enable security monitoring
- [ ] Centralize logs
- [ ] Monitor resource usage
- [ ] Set up alerting

### Security Automation

#### CI/CD Security Integration
```yaml
# .github/workflows/security.yml
name: Security Scan

on: [push, pull_request]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build image
        run: docker build -t myapp:${{ github.sha }} .
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'myapp:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
```

## Compliance and Standards

### Security Standards
- **CIS Docker Benchmark**: Industry-standard security configuration
- **NIST Container Security**: Federal guidelines for container security
- **PCI DSS**: Payment card industry security standards
- **SOC 2**: Security and availability standards

### Compliance Tools
```bash
# Docker Bench Security
docker run -it --net host --pid host --userns host --cap-add audit_control \
  -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
  -v /var/lib:/var/lib:ro \
  -v /var/run/docker.sock:/var/run/docker.sock:ro \
  -v /usr/lib/systemd:/usr/lib/systemd:ro \
  -v /etc:/etc:ro --label docker_bench_security \
  docker/docker-bench-security

# InSpec compliance testing
inspec exec https://github.com/dev-sec/cis-docker-benchmark
```

## Incident Response

### Security Incident Handling
```bash
# Stop compromised container immediately
docker stop compromised-container

# Preserve container for forensics
docker commit compromised-container evidence-image

# Inspect container logs
docker logs compromised-container > incident-logs.txt

# Network analysis
docker network inspect network-name
netstat -tulpn | grep docker

# Process analysis
docker exec compromised-container ps aux
docker top compromised-container
```

### Recovery Procedures
```bash
# Remove compromised containers
docker rm -f compromised-container

# Remove compromised images
docker rmi compromised-image

# Clean up networks
docker network prune

# Rebuild from clean images
docker-compose down
docker-compose pull
docker-compose up -d
```

---

*Container security requires a layered approach and continuous vigilance. Implement these practices to build secure, resilient containerized applications.*
