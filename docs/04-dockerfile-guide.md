# Dockerfile Guide

## What is a Dockerfile?

A Dockerfile is a text file containing a series of instructions used to build a Docker image automatically. Each instruction creates a new layer in the image, making the build process efficient and cacheable.

## Dockerfile Structure

### Basic Syntax
```dockerfile
# Comment
INSTRUCTION arguments
```

### Example Dockerfile
```dockerfile
# Use official Node.js runtime as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Expose port
EXPOSE 3000

# Define default command
CMD ["npm", "start"]
```

## Dockerfile Instructions

### FROM - Base Image
```dockerfile
# Official image
FROM node:18-alpine

# Specific version
FROM ubuntu:20.04

# Multi-stage build
FROM node:18-alpine AS builder
FROM nginx:alpine AS runtime
```

### WORKDIR - Working Directory
```dockerfile
# Set working directory
WORKDIR /app

# All subsequent commands run from /app
COPY . .
RUN npm install
```

### COPY vs ADD
```dockerfile
# COPY - Simple file/directory copying
COPY package.json ./
COPY src/ ./src/
COPY . .

# ADD - Advanced copying with URL and tar extraction
ADD https://example.com/file.tar.gz /tmp/
ADD archive.tar.gz /opt/  # Automatically extracts
```

**Best Practice**: Use COPY unless you need ADD's special features.

### RUN - Execute Commands
```dockerfile
# Single command
RUN npm install

# Multiple commands (better for caching)
RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Using arrays (exec form)
RUN ["npm", "install"]
```

### CMD vs ENTRYPOINT
```dockerfile
# CMD - Default command (can be overridden)
CMD ["npm", "start"]
CMD npm start

# ENTRYPOINT - Always executed (not overridden)
ENTRYPOINT ["npm", "start"]

# Combined usage
ENTRYPOINT ["npm"]
CMD ["start"]
# Results in: npm start
# Can override CMD: docker run myapp test -> npm test
```

### EXPOSE - Port Declaration
```dockerfile
# Expose single port
EXPOSE 3000

# Expose multiple ports
EXPOSE 3000 8080

# Expose with protocol
EXPOSE 3000/tcp
EXPOSE 53/udp
```

### ENV - Environment Variables
```dockerfile
# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV DEBUG=false

# Multiple variables
ENV NODE_ENV=production \
    PORT=3000 \
    DEBUG=false
```

### ARG - Build Arguments
```dockerfile
# Define build argument
ARG VERSION=latest
ARG BUILD_DATE

# Use in FROM
FROM node:${VERSION}

# Use in RUN
RUN echo "Build date: ${BUILD_DATE}"
```

Build with arguments:
```bash
docker build --build-arg VERSION=18-alpine --build-arg BUILD_DATE=$(date) -t myapp .
```

### VOLUME - Mount Points
```dockerfile
# Declare volume mount points
VOLUME ["/data"]
VOLUME ["/var/log", "/var/db"]

# Single volume
VOLUME /app/uploads
```

### USER - Set User
```dockerfile
# Create user and group
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Switch to user
USER nextjs

# Switch to user:group
USER 1001:1001
```

### HEALTHCHECK - Health Monitoring
```dockerfile
# HTTP health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Custom health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ["node", "healthcheck.js"]

# Disable health check
HEALTHCHECK NONE
```

## Multi-Stage Builds

### Basic Multi-Stage
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS production
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY package*.json ./
EXPOSE 3000
CMD ["npm", "start"]
```

### Advanced Multi-Stage
```dockerfile
# Dependencies stage
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Runtime stage
FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY package*.json ./
USER 1001
EXPOSE 3000
CMD ["npm", "start"]
```

### Build Specific Stage
```bash
# Build only the builder stage
docker build --target builder -t myapp:builder .

# Build production stage
docker build --target runtime -t myapp:prod .
```

## Best Practices

### 1. Use Appropriate Base Images
```dockerfile
# Good - Minimal base image
FROM node:18-alpine

# Better - Distroless for production
FROM gcr.io/distroless/nodejs18-debian11

# Avoid - Large base images
FROM ubuntu:latest  # Too large
```

### 2. Optimize Layer Caching
```dockerfile
# Good - Dependencies first (cached if unchanged)
COPY package*.json ./
RUN npm ci
COPY . .

# Bad - Code changes invalidate dependency cache
COPY . .
RUN npm ci
```

### 3. Minimize Layers
```dockerfile
# Good - Combined commands
RUN apt-get update && \
    apt-get install -y curl wget && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Bad - Multiple layers
RUN apt-get update
RUN apt-get install -y curl
RUN apt-get install -y wget
RUN apt-get clean
```

### 4. Use .dockerignore
```dockerignore
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
Dockerfile
.dockerignore
```

### 5. Security Best Practices
```dockerfile
# Don't run as root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001
USER nextjs

# Use specific versions
FROM node:18.17.0-alpine

# Don't expose unnecessary ports
EXPOSE 3000  # Only what's needed

# Use COPY instead of ADD
COPY . .  # More predictable than ADD
```

## Language-Specific Examples

### Node.js Application
```dockerfile
FROM node:18-alpine

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy app source
COPY --chown=nextjs:nodejs . .

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node healthcheck.js

# Start application
CMD ["node", "server.js"]
```

### Python Application
```dockerfile
FROM python:3.11-alpine

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache gcc musl-dev

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create non-root user
RUN adduser -D -s /bin/sh appuser

# Copy application
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Run application
CMD ["python", "app.py"]
```

### Go Application
```dockerfile
# Build stage
FROM golang:1.21-alpine AS builder

# Install git (required for some Go modules)
RUN apk add --no-cache git

# Set working directory
WORKDIR /app

# Copy go mod files first (for better caching)
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Verify dependencies
RUN go mod verify

# Copy source code
COPY . .

# Build application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main .

# Runtime stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/main .

# Change ownership to non-root user
RUN chown appuser:appgroup main

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run application
CMD ["./main"]
```

### Go Web API with Fiber (Advanced)
```dockerfile
# Build stage
FROM golang:1.21-alpine AS builder

# Set build arguments
ARG VERSION=dev
ARG BUILD_TIME
ARG COMMIT_SHA

# Install dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build with version information
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s -X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.CommitSHA=${COMMIT_SHA}" \
    -a -installsuffix cgo \
    -o main .

# Runtime stage
FROM scratch

# Copy ca-certificates from builder
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy timezone data
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# Copy binary
COPY --from=builder /app/main /main

# Expose port
EXPOSE 3000

# Run application
ENTRYPOINT ["/main"]
```

### .NET Application
```dockerfile
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build

# Set working directory
WORKDIR /src

# Copy project files
COPY ["MyApp.csproj", "./"]

# Restore dependencies
RUN dotnet restore "MyApp.csproj"

# Copy source code
COPY . .

# Build application
RUN dotnet build "MyApp.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "MyApp.csproj" -c Release -o /app/publish \
    --no-restore --no-build

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime

# Install culture support (for globalization)
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# Create non-root user
RUN addgroup -g 1001 -S dotnetgroup && \
    adduser -u 1001 -S dotnetuser -G dotnetgroup

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Change ownership to non-root user
RUN chown -R dotnetuser:dotnetgroup /app

# Switch to non-root user
USER dotnetuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set entry point
ENTRYPOINT ["dotnet", "MyApp.dll"]
```

### .NET Web API (Advanced)
```dockerfile
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

# Set build arguments
ARG BUILD_CONFIGURATION=Release
ARG VERSION=1.0.0

# Set working directory
WORKDIR /src

# Copy solution file
COPY ["MyApp.sln", "./"]

# Copy project files
COPY ["src/MyApp.Api/MyApp.Api.csproj", "src/MyApp.Api/"]
COPY ["src/MyApp.Core/MyApp.Core.csproj", "src/MyApp.Core/"]
COPY ["src/MyApp.Infrastructure/MyApp.Infrastructure.csproj", "src/MyApp.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "MyApp.sln"

# Copy source code
COPY . .

# Build solution
RUN dotnet build "MyApp.sln" -c $BUILD_CONFIGURATION --no-restore

# Test stage (optional)
FROM build AS test
RUN dotnet test "MyApp.sln" -c $BUILD_CONFIGURATION --no-build --verbosity normal

# Publish stage
FROM build AS publish
RUN dotnet publish "src/MyApp.Api/MyApp.Api.csproj" \
    -c $BUILD_CONFIGURATION \
    -o /app/publish \
    --no-restore \
    --no-build \
    /p:Version=$VERSION

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime

# Install required packages
RUN apk add --no-cache \
    icu-libs \
    curl

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false \
    ASPNETCORE_URLS=http://+:8080 \
    ASPNETCORE_ENVIRONMENT=Production

# Create non-root user
RUN addgroup -g 1001 -S dotnetgroup && \
    adduser -u 1001 -S dotnetuser -G dotnetgroup

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Change ownership
RUN chown -R dotnetuser:dotnetgroup /app

# Switch to non-root user
USER dotnetuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set entry point
ENTRYPOINT ["dotnet", "MyApp.Api.dll"]
```

### .NET Minimal API
```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
WORKDIR /src

# Copy and restore
COPY ["MinimalApi.csproj", "./"]
RUN dotnet restore

# Copy and build
COPY . .
RUN dotnet publish -c Release -o /app/publish

# Runtime
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine
WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN adduser -u 1001 -D -s /bin/sh dotnetuser

# Copy app
COPY --from=build /app/publish .
RUN chown -R dotnetuser:dotnetuser /app

USER dotnetuser

EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080

HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["dotnet", "MinimalApi.dll"]
```

## Advanced Techniques

### Build Arguments for Flexibility
```dockerfile
ARG NODE_VERSION=18
ARG ALPINE_VERSION=3.18

FROM node:${NODE_VERSION}-alpine${ALPINE_VERSION}

ARG BUILD_DATE
ARG VERSION
ARG COMMIT_SHA

LABEL build_date=${BUILD_DATE} \
      version=${VERSION} \
      commit=${COMMIT_SHA}
```

### Conditional Instructions
```dockerfile
ARG ENVIRONMENT=production

# Install dev dependencies only in development
RUN if [ "$ENVIRONMENT" = "development" ]; then \
        npm install; \
    else \
        npm ci --only=production; \
    fi
```

### Using Build Secrets
```dockerfile
# Mount secret during build
RUN --mount=type=secret,id=npm_token \
    echo "//registry.npmjs.org/:_authToken=$(cat /run/secrets/npm_token)" > ~/.npmrc && \
    npm install && \
    rm ~/.npmrc
```

Build with secret:
```bash
echo "your_npm_token" | docker build --secret id=npm_token,src=- -t myapp .
```

## Troubleshooting

### Common Issues
```dockerfile
# Issue: Large image size
# Solution: Use multi-stage builds and minimal base images

# Issue: Slow builds
# Solution: Optimize layer caching and use .dockerignore

# Issue: Permission errors
# Solution: Use proper USER instruction

# Issue: Application not accessible
# Solution: Check EXPOSE and port mapping
```

### Debugging Techniques
```bash
# Build with verbose output
docker build --progress=plain -t myapp .

# Inspect intermediate layers
docker run -it <intermediate_image_id> /bin/sh

# Build specific stage for debugging
docker build --target builder -t debug .
docker run -it debug /bin/sh
```

## Best Practices Summary

1. **Use minimal base images** (Alpine, distroless)
2. **Optimize layer caching** (dependencies first)
3. **Minimize layers** (combine RUN commands)
4. **Use .dockerignore** to exclude unnecessary files
5. **Don't run as root** (create and use non-root user)
6. **Use specific image tags** (avoid `latest`)
7. **Implement health checks** for production
8. **Use multi-stage builds** for optimization
9. **Set appropriate environment variables**
10. **Add meaningful labels** for metadata

---

*Mastering Dockerfile writing is essential for creating efficient, secure, and maintainable container images.*
