# Docker Containers

## What are Docker Containers?

Docker containers are lightweight, portable, and isolated runtime environments created from Docker images. They package an application with all its dependencies and run consistently across different environments.

## Container Fundamentals

### Container vs Virtual Machine

| Feature | Container | Virtual Machine |
|---------|-----------|-----------------|
| **OS** | Shares host OS kernel | Full guest OS |
| **Size** | MBs | GBs |
| **Startup** | Seconds | Minutes |
| **Resource Usage** | Low overhead | High overhead |
| **Isolation** | Process-level | Hardware-level |
| **Portability** | High | Medium |

### Container Architecture

```
┌─────────────────────────────────────────┐
│           Host Operating System          │
├─────────────────────────────────────────┤
│              Docker Engine              │
├─────────────────────────────────────────┤
│  Container 1  │  Container 2  │  Container 3  │
│  ┌─────────┐  │  ┌─────────┐  │  ┌─────────┐  │
│  │   App   │  │  │   App   │  │  │   App   │  │
│  │  Deps   │  │  │  Deps   │  │  │  Deps   │  │
│  └─────────┘  │  └─────────┘  │  └─────────┘  │
└─────────────────────────────────────────┘
```

## Container Lifecycle

### Container States

```
┌─────────┐    docker run     ┌─────────┐
│ Created │ ──────────────→   │ Running │
└─────────┘                   └─────────┘
     ↑                             │
     │ docker create               │ docker stop
     │                             ↓
┌─────────┐    docker start    ┌─────────┐
│ Stopped │ ←──────────────    │ Paused  │
└─────────┘                   └─────────┘
     │                             ↑
     │ docker rm                   │ docker pause
     ↓                             │
┌─────────┐                   ┌─────────┐
│ Removed │                   │ Running │
└─────────┘                   └─────────┘
```

### Lifecycle Commands

```bash
# Create container (without starting)
docker create --name my-container nginx

# Start existing container
docker start my-container

# Run container (create + start)
docker run --name my-container nginx

# Stop running container
docker stop my-container

# Pause/unpause container
docker pause my-container
docker unpause my-container

# Restart container
docker restart my-container

# Remove container
docker rm my-container

# Force remove running container
docker rm -f my-container
```

## Running Containers

### Basic Container Operations

```bash
# Run container in foreground
docker run nginx

# Run container in background (detached)
docker run -d nginx

# Run with custom name
docker run -d --name web-server nginx

# Run interactively
docker run -it ubuntu bash

# Run and remove after exit
docker run -it --rm ubuntu bash
```

### Port Mapping

```bash
# Map single port
docker run -d -p 8080:80 nginx

# Map multiple ports
docker run -d -p 8080:80 -p 8443:443 nginx

# Map to specific host interface
docker run -d -p 127.0.0.1:8080:80 nginx

# Map random host port
docker run -d -P nginx

# Check port mappings
docker port container_name
```

### Environment Variables

```bash
# Set single environment variable
docker run -d -e NODE_ENV=production myapp

# Set multiple environment variables
docker run -d \
  -e NODE_ENV=production \
  -e PORT=3000 \
  -e DEBUG=false \
  myapp

# Load from file
docker run -d --env-file .env myapp

# Example .env file:
# NODE_ENV=production
# PORT=3000
# DATABASE_URL=******************************/mydb
```

### Volume Mounting

```bash
# Named volume
docker run -d -v my-volume:/data nginx

# Bind mount (host directory)
docker run -d -v /host/path:/container/path nginx

# Read-only mount
docker run -d -v /host/path:/container/path:ro nginx

# Multiple volumes
docker run -d \
  -v /host/data:/app/data \
  -v /host/logs:/app/logs \
  myapp
```

## Container Management

### Listing Containers

```bash
# List running containers
docker ps

# List all containers (including stopped)
docker ps -a

# List with custom format
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# List only container IDs
docker ps -q

# Filter containers
docker ps --filter "status=running"
docker ps --filter "name=web"
docker ps --filter "ancestor=nginx"
```

### Container Information

```bash
# Inspect container details
docker inspect container_name

# Get specific information
docker inspect --format='{{.State.Status}}' container_name
docker inspect --format='{{.NetworkSettings.IPAddress}}' container_name

# Show container processes
docker top container_name

# Show resource usage statistics
docker stats container_name
docker stats  # All running containers

# Show container logs
docker logs container_name
docker logs -f container_name  # Follow logs
docker logs --tail 50 container_name  # Last 50 lines
```

### Executing Commands in Containers

```bash
# Execute single command
docker exec container_name ls -la

# Interactive shell
docker exec -it container_name bash
docker exec -it container_name sh

# Execute as specific user
docker exec -it -u root container_name bash

# Execute with environment variables
docker exec -it -e DEBUG=true container_name bash

# Execute in specific working directory
docker exec -it -w /app container_name bash
```

## Container Networking

### Network Modes

```bash
# Default bridge network
docker run -d nginx

# Host network (share host networking)
docker run -d --network host nginx

# No networking
docker run -d --network none nginx

# Custom network
docker network create my-network
docker run -d --network my-network nginx
```

### Container Communication

```bash
# Create custom network
docker network create app-network

# Run containers on same network
docker run -d --name database --network app-network postgres
docker run -d --name webapp --network app-network myapp

# Containers can communicate using container names
# webapp can connect to database using hostname "database"
```

## Resource Management

### CPU and Memory Limits

```bash
# Limit memory
docker run -d --memory="512m" nginx

# Limit CPU
docker run -d --cpus="1.5" nginx

# Limit both
docker run -d --memory="1g" --cpus="2" nginx

# Set CPU priority
docker run -d --cpu-shares=512 nginx

# Limit swap
docker run -d --memory="512m" --memory-swap="1g" nginx
```

### Storage Limits

```bash
# Limit container size
docker run -d --storage-opt size=10G nginx

# Limit read/write operations
docker run -d --device-read-bps /dev/sda:1mb nginx
docker run -d --device-write-bps /dev/sda:1mb nginx
```

## Container Security

### User Management

```bash
# Run as specific user
docker run -d --user 1001:1001 nginx

# Run as current user
docker run -d --user $(id -u):$(id -g) myapp

# Add user to groups
docker run -d --group-add audio --group-add video myapp
```

### Security Options

```bash
# Drop capabilities
docker run -d --cap-drop ALL --cap-add NET_BIND_SERVICE nginx

# Read-only root filesystem
docker run -d --read-only nginx

# No new privileges
docker run -d --security-opt no-new-privileges nginx

# AppArmor/SELinux
docker run -d --security-opt apparmor:unconfined nginx
```

## Container Monitoring

### Health Checks

```bash
# Run container with health check
docker run -d \
  --health-cmd="curl -f http://localhost/ || exit 1" \
  --health-interval=30s \
  --health-timeout=10s \
  --health-retries=3 \
  nginx

# Check health status
docker ps  # Shows health status
docker inspect --format='{{.State.Health.Status}}' container_name
```

### Logging

```bash
# View logs
docker logs container_name

# Follow logs in real-time
docker logs -f container_name

# Show timestamps
docker logs -t container_name

# Show logs since specific time
docker logs --since 2023-01-01T10:00:00 container_name

# Limit log output
docker logs --tail 100 container_name

# Configure logging driver
docker run -d --log-driver json-file --log-opt max-size=10m nginx
```

## Container Cleanup

### Removing Containers

```bash
# Remove stopped container
docker rm container_name

# Force remove running container
docker rm -f container_name

# Remove multiple containers
docker rm container1 container2 container3

# Remove all stopped containers
docker container prune

# Remove containers with filter
docker container prune --filter "until=24h"
```

### Bulk Operations

```bash
# Stop all running containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Remove all stopped containers
docker container prune

# Complete cleanup (containers, images, networks, volumes)
docker system prune -a --volumes
```

## Best Practices

### Container Design Principles

1. **One Process Per Container**: Each container should run a single process
2. **Stateless**: Containers should be stateless and ephemeral
3. **Immutable**: Don't modify running containers, rebuild images instead
4. **Minimal**: Use minimal base images and only necessary dependencies
5. **Security**: Run as non-root user when possible

### Operational Best Practices

1. **Use meaningful names** for containers
2. **Set resource limits** to prevent resource exhaustion
3. **Implement health checks** for production containers
4. **Use proper logging** configuration
5. **Regular cleanup** of unused containers
6. **Monitor resource usage** with docker stats
7. **Use init systems** for multi-process containers

### Example Production Container

```bash
docker run -d \
  --name production-app \
  --restart unless-stopped \
  --memory="1g" \
  --cpus="1" \
  --user 1001:1001 \
  --read-only \
  --tmpfs /tmp \
  -p 8080:8080 \
  -v app-data:/app/data \
  -e NODE_ENV=production \
  --health-cmd="curl -f http://localhost:8080/health || exit 1" \
  --health-interval=30s \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  myapp:v1.0.0
```

## Troubleshooting

### Common Issues

```bash
# Container won't start
docker logs container_name
docker inspect container_name

# Container exits immediately
docker run -it --entrypoint /bin/sh myapp

# Port already in use
docker ps  # Check what's using the port
netstat -tulpn | grep :8080

# Permission denied
docker exec -it --user root container_name bash

# Out of disk space
docker system df
docker system prune
```

---

*Mastering container management is essential for effective Docker usage. Practice these commands and concepts with real applications.*
